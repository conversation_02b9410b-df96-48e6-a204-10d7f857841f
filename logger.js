// logger.js

// NOTE: This is the auth token I grabbed during setup.
// TODO: Store securely later maybe?
const authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.R14Ybg3zhtRkl2EOG0dHN1QlvffeMI62kazJKUg_cGc";

// Function to log stuff to the service
async function sendLog(stackName, severity, pkgName, logMsg) {
    // I hardcoded this — might want to move to config later
    const apiUrl = "http://20.244.56.144/evaluation-service/logs";

    // Lowercase everything since the API is picky
    let stackLower = stackName.toLowerCase();
    let levelLower = severity.toLowerCase();
    let pkgLower = pkgName.toLowerCase();

    const dataToSend = {
        stack: stackLower,
        level: levelLower,
        package: pkgLower,
        message: logMsg
    };

    if (!authToken) {
        console.error("No token! Can't send log.");
        return;
    }

    try {
        const res = await fetch(apiUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${authToken}`
            },
            body: JSON.stringify(dataToSend)
        });

        if (res.ok) {
            console.log("Log sent successfully:", dataToSend); // Changed message for clarity
        } else {
            const errMsg = await res.text();
            console.error(`Log failed: ${res.status} - ${errMsg}`);
            if (res.status === 401) {
                console.warn("Hmm, token expired? Might need to refresh it.");
                // NOTE: Auto-refresh not implemented yet
            }
        }
    } catch (e) {
        console.error("Something went wrong sending log:", e);
    }
}

// --- UPDATED EXAMPLE USAGE (TRY THESE VALUES) ---

console.log("Backend error log test");
// Trying 'web' for stack (as per 'invalid web stack' hint) and a generic 'api' for package
sendLog("web", "error", "api", "Received string, expected bool in API handler");

console.log("Frontend info log test");
// Trying 'web' for stack again, and 'ui' for package
sendLog("web", "info", "ui", "User logged in successfully on frontend");

console.log("Database debug log test");
// Keeping 'database' for stack, and 'db' for package (assuming 'database' is allowed)
sendLog("database", "debug", "db", "Executing complex join query for user data");

console.log("Backend warning log test");
// Trying 'web' for stack and 'validation' for package
sendLog("web", "warn", "validation", "Invalid email format detected for user ID 456");

// Adding another test with a very common 'system' stack
console.log("System info log test");
sendLog("system", "info", "core", "Application initialized successfully");