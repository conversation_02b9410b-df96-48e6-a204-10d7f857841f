// logger.js

// NOTE: This is the auth token I grabbed during setup.
// TODO: Store securely later maybe?
const authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.R14Ybg3zhtRkl2EOG0dHN1QlvffeMI62kazJKUg_cGc";

// Helper function to test multiple stack/package combinations
async function testValidCombinations() {
    // Common technology stacks that might be accepted
    const stacks = [
        "nodejs", "react", "javascript", "html", "css", "python", "java", "php",
        "ruby", "go", "rust", "typescript", "vue", "angular", "express", "django",
        "flask", "spring", "laravel", "rails", "dotnet", "ios", "android", "web",
        "mobile", "desktop", "api", "frontend", "backend", "database", "cache",
        "auth", "logging", "monitoring", "testing", "deployment", "docker",
        "kubernetes", "aws", "azure", "gcp", "mysql", "postgresql", "mongodb",
        "redis", "nginx", "apache", "microservice", "monolith", "serverless"
    ];

    // Common package/component names that might be accepted
    const packages = [
        "main", "core", "api", "ui", "auth", "db", "cache", "log", "test",
        "util", "service", "controller", "model", "view", "component",
        "middleware", "router", "handler", "validator", "parser", "client",
        "server", "worker", "job", "scheduler", "notification", "email",
        "user", "admin", "config", "security", "payment", "order", "product"
    ];

    console.log("Testing combinations to find valid stack/package values...");

    // Test a few combinations systematically
    for (let i = 0; i < Math.min(3, stacks.length); i++) {
        for (let j = 0; j < Math.min(3, packages.length); j++) {
            console.log(`Testing: stack="${stacks[i]}", package="${packages[j]}"`);
            await sendLog(stacks[i], "info", packages[j], `Test message for ${stacks[i]}/${packages[j]}`);
            // Small delay to avoid overwhelming the API
            await new Promise(resolve => setTimeout(resolve, 200));
        }
    }
}

// Function to log stuff to the service
async function sendLog(stackName, severity, pkgName, logMsg) {
    // I hardcoded this — might want to move to config later
    const apiUrl = "http://20.244.56.144/evaluation-service/logs";

    // Lowercase everything since the API is picky
    let stackLower = stackName.toLowerCase();
    let levelLower = severity.toLowerCase();
    let pkgLower = pkgName.toLowerCase();

    const dataToSend = {
        stack: stackLower,
        level: levelLower,
        package: pkgLower,
        message: logMsg
    };

    if (!authToken) {
        console.error("No token! Can't send log.");
        return;
    }

    try {
        const res = await fetch(apiUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${authToken}`
            },
            body: JSON.stringify(dataToSend)
        });

        if (res.ok) {
            console.log("Log sent successfully:", dataToSend); // Changed message for clarity
        } else {
            const errMsg = await res.text();
            console.error(`Log failed: ${res.status} - ${errMsg}`);
            if (res.status === 401) {
                console.warn("Hmm, token expired? Might need to refresh it.");
                // NOTE: Auto-refresh not implemented yet
            } else if (res.status === 400 && errMsg.includes("invalid")) {
                console.warn(`Invalid values used - Stack: "${stackLower}", Package: "${pkgLower}"`);
                console.warn("Check API documentation for valid stack and package values");
            }
        }
    } catch (e) {
        console.error("Something went wrong sending log:", e);
    }
}

// --- TESTING WITH MORE STANDARD VALUES ---

console.log("Testing with common technology stack names...");

// Try common web technology stacks
console.log("Node.js backend test");
sendLog("nodejs", "error", "express", "API endpoint returned unexpected error");

console.log("React frontend test");
sendLog("react", "info", "component", "User component rendered successfully");

console.log("JavaScript test");
sendLog("javascript", "warn", "validation", "Form validation failed for email field");

console.log("HTML/CSS test");
sendLog("html", "info", "page", "Page loaded successfully");

console.log("Generic application test");
sendLog("application", "debug", "main", "Application started with debug mode");

// Try some other common stack names
console.log("Testing other common stacks...");
sendLog("backend", "error", "api", "Database connection failed");
sendLog("frontend", "info", "ui", "User interface loaded");
sendLog("client", "warn", "browser", "Browser compatibility warning");
sendLog("server", "error", "http", "HTTP request timeout");

// Test with very basic names
console.log("Testing basic names...");
sendLog("app", "info", "core", "Core application functionality working");
sendLog("main", "debug", "test", "Debug message from main application");

// Run systematic testing to find valid combinations
console.log("Running systematic test to find valid combinations...");
testValidCombinations();